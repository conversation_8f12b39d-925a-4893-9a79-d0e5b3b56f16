# ===========================================
# 开发环境配置 (Development Environment)
# ===========================================

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USER=admin
DB_PASSWORD=admin
DB_NAME=what_to_wear_dev
DB_SSLMODE=disable

# JWT配置
JWT_SECRET=dev-jwt-secret-key-not-for-production

# 日志配置 - 开发环境优化
LOG_LEVEL=debug
LOG_FORMAT=text
LOG_OUTPUT=stdout
LOG_TO_FILE=false
LOG_FILE_PATH=logs/dev.log
LOG_MAX_SIZE=50
LOG_MAX_BACKUPS=2
LOG_MAX_AGE=3
LOG_COMPRESS=false

# 服务器配置
GIN_MODE=debug
PORT=8080
HOST=localhost

# CORS配置 - 开发环境宽松设置
CORS_ORIGINS=http://localhost:1420,http://localhost:3000,http://localhost:5173
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
CORS_HEADERS=Origin,Content-Type,Authorization,X-Request-ID,Accept
CORS_EXPOSE_HEADERS=Content-Length,X-Request-ID
CORS_CREDENTIALS=true

# 开发环境特殊配置
ENABLE_DETAILED_LOGGING=true
ENABLE_SQL_LOGGING=true
API_TIMEOUT=60
ENABLE_METRICS=false
ENABLE_HEALTH_CHECK=true
MAX_CONNECTIONS=100
MAX_REQUEST_SIZE=50
