# ===========================================
# 数据库配置 (Database Configuration)
# ===========================================
DB_HOST=localhost
DB_PORT=5432
DB_USER=admin
DB_PASSWORD=admin
DB_NAME=what_to_wear
DB_SSLMODE=disable

# ===========================================
# JWT 认证配置 (JWT Authentication)
# ===========================================
JWT_SECRET=YOUR-JWT-SECRET-CHANGE-THIS-IN-PRODUCTION

# ===========================================
# 日志配置 (Logging Configuration)
# ===========================================
# 日志级别: debug, info, warn, error, fatal
LOG_LEVEL=info

# 日志格式: json (生产环境推荐) 或 text (开发环境友好)
LOG_FORMAT=json

# 日志输出方式: stdout (仅控制台), file (仅文件), both (同时输出)
LOG_OUTPUT=both

# 是否输出到文件 (true/false)
LOG_TO_FILE=true

# 日志文件路径
LOG_FILE_PATH=logs/app.log

# 日志文件最大大小 (MB)
LOG_MAX_SIZE=100

# 保留的日志备份文件数量
LOG_MAX_BACKUPS=3

# 日志文件保留天数
LOG_MAX_AGE=7

# 是否压缩旧日志文件 (true/false)
LOG_COMPRESS=true

# ===========================================
# 服务器配置 (Server Configuration)
# ===========================================
# 运行模式: debug (开发), release (生产), test (测试)
GIN_MODE=release

# 服务器端口
PORT=8080

# 服务器主机地址
HOST=0.0.0.0

# ===========================================
# CORS 跨域配置 (CORS Configuration)
# ===========================================
# 允许的源地址 (用逗号分隔)
CORS_ORIGINS=http://localhost:1420,https://tauri.localhost,http://localhost:3000

# 允许的HTTP方法 (用逗号分隔)
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH

# 允许的请求头 (用逗号分隔)
CORS_HEADERS=Origin,Content-Type,Authorization,X-Request-ID,Accept

# 暴露的响应头 (用逗号分隔)
CORS_EXPOSE_HEADERS=Content-Length,X-Request-ID

# 是否允许携带凭证 (true/false)
CORS_CREDENTIALS=true

# ===========================================
# 开发环境配置 (Development Configuration)
# ===========================================
# 是否启用详细日志 (包含请求体和响应体)
ENABLE_DETAILED_LOGGING=false

# 是否启用SQL查询日志
ENABLE_SQL_LOGGING=false

# API请求超时时间 (秒)
API_TIMEOUT=30

# ===========================================
# 生产环境配置 (Production Configuration)
# ===========================================
# 是否启用性能监控
ENABLE_METRICS=false

# 是否启用健康检查端点
ENABLE_HEALTH_CHECK=true

# 最大并发连接数
MAX_CONNECTIONS=1000

# 请求体最大大小 (MB)
MAX_REQUEST_SIZE=10