# ===========================================
# 生产环境配置 (Production Environment)
# ===========================================

# 数据库配置
DB_HOST=your-production-db-host
DB_PORT=5432
DB_USER=your-production-db-user
DB_PASSWORD=your-production-db-password
DB_NAME=what_to_wear_prod
DB_SSLMODE=require

# JWT配置
JWT_SECRET=your-super-secure-jwt-secret-key-change-this

# 日志配置 - 生产环境优化
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=both
LOG_TO_FILE=true
LOG_FILE_PATH=/var/log/what-to-wear/app.log
LOG_MAX_SIZE=200
LOG_MAX_BACKUPS=10
LOG_MAX_AGE=30
LOG_COMPRESS=true

# 服务器配置
GIN_MODE=release
PORT=8080
HOST=0.0.0.0

# CORS配置 - 生产环境严格设置
CORS_ORIGINS=https://your-production-domain.com
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Origin,Content-Type,Authorization,X-Request-ID
CORS_EXPOSE_HEADERS=Content-Length,X-Request-ID
CORS_CREDENTIALS=true

# 生产环境特殊配置
ENABLE_DETAILED_LOGGING=false
ENABLE_SQL_LOGGING=false
API_TIMEOUT=30
ENABLE_METRICS=true
ENABLE_HEALTH_CHECK=true
MAX_CONNECTIONS=1000
MAX_REQUEST_SIZE=10
