package config

import (
	"os"
	"what-to-wear/server/logger"

	"github.com/gin-gonic/gin"
)

// InitLogger 初始化日志系统
func InitLogger() {
	// 根据环境变量设置日志级别
	level := logger.InfoLevel
	if gin.Mode() == gin.DebugMode {
		level = logger.DebugLevel
	}

	// 根据环境变量设置输出方式
	output := "stdout"
	if os.Getenv("LOG_TO_FILE") == "true" {
		output = "both"
	}

	config := &logger.Config{
		Level:      level,
		Format:     getLogFormat(),
		Output:     output,
		FilePath:   getLogFilePath(),
		MaxSize:    getLogMaxSize(),
		MaxBackups: getLogMaxBackups(),
		MaxAge:     getLogMaxAge(),
		Compress:   getLogCompress(),
	}

	logger.Init(config)
}

// getLogFormat 获取日志格式
func getLogFormat() string {
	format := os.Getenv("LOG_FORMAT")
	if format == "" {
		if gin.Mode() == gin.DebugMode {
			return "text"
		}
		return "json"
	}
	return format
}

// getLogFilePath 获取日志文件路径
func getLogFilePath() string {
	path := os.Getenv("LOG_FILE_PATH")
	if path == "" {
		return "logs/app.log"
	}
	return path
}

// getLogMaxSize 获取日志文件最大大小
func getLogMaxSize() int {
	// 默认100MB
	return 100
}

// getLogMaxBackups 获取日志备份文件数量
func getLogMaxBackups() int {
	// 默认保留3个备份文件
	return 3
}

// getLogMaxAge 获取日志保留天数
func getLogMaxAge() int {
	// 默认保留7天
	return 7
}

// getLogCompress 获取是否压缩日志
func getLogCompress() bool {
	// 默认压缩
	return true
}
